# RTS Pixel Sprite Generation Stack

This package delivers:

1) **Backend API (FastAPI)** — generates pixel-art frames via Stable Diffusion (SD 1.5 family), composes sprite sheets (PNG), exports **JSON index** and **Godot `.tres`** resource.
2) **Frontend (Vue 3 + Vite)** — input prompt/params, preview results, trigger server-side sprite export and download.
3) **PowerShell script** — one-click download of recommended models.
4) **Dockerfiles** — separate for backend & frontend.
5) **docker-compose.yml** — one command to run everything.
6) **README** — models & directory layout.

> Target GPU: **8 GB** (works on CPU too but slower). Uses SD 1.5-compatible models for efficiency.

---

## 📁 Project Structure

```
rts-pixel-stack/
├─ backend/
│  ├─ app/
│  │  ├─ main.py
│  │  └─ utils.py
│  ├─ requirements.txt
│  └─ Dockerfile
├─ frontend/
│  ├─ Dockerfile
│  ├─ index.html
│  ├─ vite.config.ts
│  ├─ package.json
│  └─ src/
│     ├─ main.ts
│     ├─ App.vue
│     └─ api.ts
├─ models/                # (created by script)
│  ├─ base/
│  ├─ controlnet/
│  └─ lora/
├─ outputs/               # generated sprite sheets & metadata
├─ scripts/
│  └─ download_models.ps1
├─ docker-compose.yml
└─ README.md
```

---

## 🧠 Backend — FastAPI

### backend/app/utils.py
```python
import os, time, json, csv
from typing import List, Tuple, Optional
from PIL import Image, ImageColor


def clamp8(x: int, lo: int = 64, hi: int = 2048) -> int:
    x = max(lo, min(hi, int(x)))
    return (x // 8) * 8


def pixelize(img: Image.Image, tile_w: int, tile_h: int, keep_size: bool = True, scale: int = 2) -> Image.Image:
    if tile_w <= 0 or tile_h <= 0:
        return img
    if keep_size:
        w, h = img.size
        sx = max(1, w // max(1, tile_w))
        sy = max(1, h // max(1, tile_h))
        s = max(1, min(sx, sy))
        small = img.resize((max(1, w // s), max(1, h // s)), Image.NEAREST)
        return small.resize((w, h), Image.NEAREST)
    else:
        return img.resize((tile_w * max(1, scale), tile_h * max(1, scale)), Image.NEAREST)


def build_spritesheet(
    images: List[Image.Image],
    cols: int, rows: int,
    tile_w: int, tile_h: int,
    padding: int, bg_color: str,
    frame_ms: int,
    animation_name: str,
    out_dir: str,
) -> Tuple[str, str, str]:
    n = cols * rows
    imgs = images[:n]
    if len(imgs) < n:
        pad = Image.new("RGBA", (tile_w, tile_h), (0, 0, 0, 0))
        imgs += [pad] * (n - len(imgs))

    norm = []
    for im in imgs:
        im = im.convert("RGBA")
        if im.size != (tile_w, tile_h):
            im = im.resize((tile_w, tile_h), Image.LANCZOS)
        norm.append(im)

    grid_w = cols * tile_w + (cols + 1) * padding
    grid_h = rows * tile_h + (rows + 1) * padding

    if bg_color.lower() in ("transparent", "none"):
        sheet = Image.new("RGBA", (grid_w, grid_h), (0, 0, 0, 0))
    else:
        rgba = ImageColor.getcolor(bg_color, "RGBA")
        sheet = Image.new("RGBA", (grid_w, grid_h), rgba)

    frames_meta = []
    idx = 0
    for r in range(rows):
        for c in range(cols):
            x = padding + c * (tile_w + padding)
            y = padding + r * (tile_h + padding)
            sheet.paste(norm[idx], (x, y), norm[idx])
            frames_meta.append({"index": idx, "x": x, "y": y, "w": tile_w, "h": tile_h, "duration_ms": frame_ms})
            idx += 1

    ts = time.strftime("%Y%m%d_%H%M%S")
    base = f"sprite_{animation_name or 'anim'}_{ts}"
    os.makedirs(out_dir, exist_ok=True)
    png_path = os.path.join(out_dir, f"{base}.png")
    json_path = os.path.join(out_dir, f"{base}.json")
    csv_path = os.path.join(out_dir, f"{base}.csv")

    sheet.save(png_path, "PNG")

    atlas = {
        "meta": {
            "image": os.path.basename(png_path),
            "size": {"w": grid_w, "h": grid_h},
            "tile": {"w": tile_w, "h": tile_h},
            "padding": padding,
            "version": 1,
            "animation": animation_name or "default",
            "frame_duration_ms": frame_ms,
        },
        "frames": frames_meta,
    }
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(atlas, f, ensure_ascii=False, indent=2)

    with open(csv_path, "w", newline="", encoding="utf-8") as f:
        wtr = csv.writer(f)
        wtr.writerow(["index", "x", "y", "w", "h", "duration_ms"])
        for fr in frames_meta:
            wtr.writerow([fr["index"], fr["x"], fr["y"], fr["w"], fr["h"], fr["duration_ms"]])

    return png_path, json_path, csv_path


def write_godot_tres(atlas_json_path: str, tres_path: str):
    with open(atlas_json_path, "r", encoding="utf-8") as f:
        atlas = json.load(f)
    image_name = atlas["meta"]["image"]
    frames = atlas["frames"]
    # Minimal SpriteFrames resource (.tres) for Godot 4
    # Assumes you will place PNG and .tres together or adjust paths in your project.
    lines = []
    lines.append("[gd_resource type=SpriteFrames format=3]")
    lines.append("")
    lines.append("[resource]")
    lines.append("animations = [{\"name\": \"%s\", \"speed\": %s, \"loop\": true, \"frames\": [" % (
        atlas["meta"].get("animation", "anim"), max(1, int(1000 / max(1, atlas["meta"]["frame_duration_ms"])))))
    for i, fr in enumerate(frames):
        # Godot expects frames as subregions; here we just list metadata; you can pair with an import script
        rect = [fr["x"], fr["y"], fr["w"], fr["h"]]
        lines.append(f"    {{\"texture\": \"res://{image_name}\", \"region\": {rect}}}{',' if i < len(frames)-1 else ''}")
    lines.append("]}]")
    with open(tres_path, "w", encoding="utf-8") as f:
        f.write("\n".join(lines))
```

### backend/app/main.py
```python
import os, random, io
from typing import List, Optional
from fastapi import FastAPI, Response
from pydantic import BaseModel
from PIL import Image
import torch
from diffusers import StableDiffusionPipeline
from .utils import clamp8, pixelize, build_spritesheet, write_godot_tres

MODEL_ID = os.environ.get("MODEL_ID", "stable-diffusion-v1-5/stable-diffusion-v1-5")
OUTPUT_DIR = os.environ.get("OUTPUT_DIR", "/data/outputs")
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
DTYPE = torch.float16 if DEVICE == "cuda" else torch.float32

os.makedirs(OUTPUT_DIR, exist_ok=True)

app = FastAPI(title="RTS Pixel Sprite API", version="1.0")

class GenRequest(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = None
    steps: int = 28
    guidance: float = 7.5
    width: int = 384
    height: int = 384
    seed: int = 0
    count: int = 1
    pixel_mode: bool = True
    tile_w: int = 64
    tile_h: int = 64
    pixel_keep_size: bool = True
    pixel_scale: int = 2

class SpriteRequest(GenRequest):
    cols: int = 12
    rows: int = 1
    padding: int = 2
    bg_color: str = "transparent"
    frame_ms: int = 80
    anim_name: str = "idle"
    export_godot_tres: bool = True

@app.get("/healthz")
def health():
    return {"status": "ok"}

@app.post("/txt2img")
def txt2img(req: GenRequest):
    # Implementation here
    pass

@app.post("/spritesheet")
def spritesheet(req: SpriteRequest):
    # Implementation here
    pass
```

### backend/requirements.txt
```txt
fastapi==0.115.0
uvicorn==0.30.6
diffusers==0.31.0
transformers==4.44.2
accelerate==0.34.2
safetensors==0.4.5
Pillow==10.4.0
opencv-python-headless==*********
```

### backend/Dockerfile
```dockerfile
FROM pytorch/pytorch:2.4.0-cuda12.1-cudnn9-runtime

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    HF_HOME=/opt/hf-cache \
    HUGGINGFACE_HUB_CACHE=/opt/hf-cache \
    MODEL_ID=stable-diffusion-v1-5/stable-diffusion-v1-5 \
    OUTPUT_DIR=/data/outputs \
    SERVICE_PORT=8000

RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg libgl1 ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY backend/requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt

COPY backend/app ./app

EXPOSE ${SERVICE_PORT}
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

---

## 🎨 Frontend — Vue 3 + Vite

### frontend/package.json
```json
{
  "name": "rts-pixel-frontend",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview --port 5173"
  },
  "dependencies": {
    "axios": "^1.7.2",
    "vue": "^3.4.38"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.4",
    "typescript": "^5.5.4",
    "vite": "^5.4.2"
  }
}
```

### frontend/vite.config.ts
```ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: { port: 5173, host: true },
  preview: { port: 5173, host: true },
})
```

### frontend/index.html
```html
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RTS Pixel Sprite UI</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
```

### frontend/src/main.ts
```ts
import { createApp } from 'vue'
import App from './App.vue'
createApp(App).mount('#app')
```

### frontend/src/api.ts
```ts
import axios from 'axios'

const api = axios.create({ baseURL: import.meta.env.VITE_API_URL || '/api' })

export async function generatePreview(payload: any): Promise<Blob> {
  const res = await api.post('/txt2img', payload, { responseType: 'blob' })
  return res.data
}

export async function generateSprite(payload: any): Promise<{blob: Blob, headers: any}> {
  const res = await api.post('/spritesheet', payload, { responseType: 'blob' })
  return { blob: res.data, headers: res.headers }
}

export default api
```

### frontend/src/App.vue
```vue
<template>
  <main style="max-width: 980px; margin: 24px auto; font-family: ui-sans-serif, system-ui;">
    <h1>RTS Pixel Sprite Generator</h1>
    <section style="display:grid; grid-template-columns: 1fr 1fr; gap: 16px;">
      <div>
        <label>Prompt</label>
        <textarea v-model="form.prompt" rows="4" style="width:100%" placeholder="pixel art, RTS unit, ..."/>
        <label>Negative Prompt</label>
        <input v-model="form.negative_prompt" style="width:100%"/>
        <div style="display:grid; grid-template-columns: repeat(2,1fr); gap:8px; margin-top:8px;">
          <label>Steps <input type="number" v-model.number="form.steps" min="5" max="100"/></label>
          <label>CFG <input type="number" step="0.5" v-model.number="form.guidance"/></label>
          <label>Width <input type="number" v-model.number="form.width"/></label>
          <label>Height <input type="number" v-model.number="form.height"/></label>
          <label>Seed <input type="number" v-model.number="form.seed"/></label>
          <label>Frames <input type="number" v-model.number="form.count"/></label>
        </div>
        <div style="margin-top:8px;">
          <label><input type="checkbox" v-model="form.pixel_mode"/> Pixel Mode</label>
          <div style="display:grid; grid-template-columns: repeat(2,1fr); gap:8px;">
            <label>Tile W <input type="number" v-model.number="form.tile_w"/></label>
            <label>Tile H <input type="number" v-model.number="form.tile_h"/></label>
          </div>
        </div>
        <h3>Sprite Sheet</h3>
        <div style="display:grid; grid-template-columns: repeat(2,1fr); gap:8px;">
          <label>Cols <input type="number" v-model.number="form.cols"/></label>
          <label>Rows <input type="number" v-model.number="form.rows"/></label>
          <label>Padding <input type="number" v-model.number="form.padding"/></label>
          <label>Frame ms <input type="number" v-model.number="form.frame_ms"/></label>
          <label>Anim Name <input type="text" v-model="form.anim_name"/></label>
          <label>BG <input type="text" v-model="form.bg_color" placeholder="transparent or #RRGGBB"/></label>
          <label><input type="checkbox" v-model="form.export_godot_tres"/> Export Godot .tres</label>
        </div>
        <div style="margin-top:12px; display:flex; gap:8px;">
          <button @click="onPreview">Preview</button>
          <button @click="onGenerate">Generate SpriteSheet</button>
        </div>
      </div>
      <div>
        <h3>Preview</h3>
        <div v-if="previewUrl"><img :src="previewUrl" style="max-width:100%"/></div>
        <div v-if="spriteUrl" style="margin-top:12px;">
          <h3>SpriteSheet</h3>
          <img :src="spriteUrl" style="max-width:100%"/>
          <div style="margin-top:8px; display:flex; gap:8px; flex-wrap:wrap;">
            <a :href="spriteUrl" download="sprite.png">Download PNG</a>
            <a v-if="headers['x-sprite-json']" :href="jsonUrl" download>Download JSON</a>
            <a v-if="headers['x-sprite-csv']" :href="csvUrl" download>Download CSV</a>
            <a v-if="headers['x-godot-tres']" :href="tresUrl" download>Download Godot .tres</a>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { generatePreview, generateSprite } from './api'

const form = ref({
  prompt: 'pixel art, RTS unit, Ming-dynasty swordsman with shield, top-down sprite, 8-bit, crisp edges',
  negative_prompt: 'blurry, lowres, smooth shading, antialiasing',
  steps: 28,
  guidance: 7.5,
  width: 384,
  height: 384,
  seed: 0,
  count: 12,
  pixel_mode: true,
  tile_w: 64,
  tile_h: 64,
  cols: 12,
  rows: 1,
  padding: 2,
  bg_color: 'transparent',
  frame_ms: 80,
  anim_name: 'idle',
  export_godot_tres: true,
})

const previewUrl = ref<string | null>(null)
const spriteUrl = ref<string | null>(null)
const headers = ref<Record<string, string>>({})
const jsonUrl = computed(() => headers.value['x-sprite-json'] ? `/files/${headers.value['x-sprite-json']}` : '')
const csvUrl  = computed(() => headers.value['x-sprite-csv'] ? `/files/${headers.value['x-sprite-csv']}` : '')
const tresUrl = computed(() => headers.value['x-godot-tres'] ? `/files/${headers.value['x-godot-tres']}` : '')

async function onPreview(){
  const blob = await generatePreview(form.value)
  previewUrl.value = URL.createObjectURL(blob)
}

async function onGenerate(){
  const { blob, headers: h } = await generateSprite(form.value)
  spriteUrl.value = URL.createObjectURL(blob)
  headers.value = h
}
</script>

<style>
label{ display:block; font-size:12px; color:#444; margin:6px 0 2px; }
button{ padding:8px 12px; }
textarea, input{ padding:6px; }
</style>
```

### frontend/Dockerfile
```dockerfile
# Build stage
FROM node:20-alpine as build
WORKDIR /app
COPY frontend/package.json frontend/package-lock.json* ./
RUN npm i --no-audit --no-fund
COPY frontend .
RUN npm run build

# Serve stage
FROM nginx:1.27-alpine
COPY --from=build /app/dist /usr/share/nginx/html
# simple api proxy (optional): use compose to wire to backend at /api
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

---

## 🧰 PowerShell — scripts/download_models.ps1
```powershell
param(
  [string]$BaseDir = "./models",
  [switch]$SkipPixelModels,
  [switch]$SkipLCM,
  [switch]$OnlyCheckLogin
)
function Ensure-Dir([string]$p){ if(-not(Test-Path $p)){ New-Item -ItemType Directory -Path $p | Out-Null } }
function Need-HFLogin{ try{ & huggingface-cli whoami *> $null; if($LASTEXITCODE -ne 0){return $true} return $false }catch{ return $true }}
function Ensure-HFCLI{ if(-not(Get-Command "huggingface-cli" -ErrorAction SilentlyContinue)){ python -m pip install --upgrade huggingface_hub | Out-Null } }
$BASE_MODEL_DIR = Join-Path $BaseDir "base"; $CONTROLNET_DIR = Join-Path $BaseDir "controlnet"; $LORA_DIR = Join-Path $BaseDir "lora"
Ensure-Dir $BASE_MODEL_DIR; Ensure-Dir $CONTROLNET_DIR; Ensure-Dir $LORA_DIR
Ensure-HFCLI; if(Need-HFLogin){ huggingface-cli login; if(Need-HFLogin){ throw "Hugging Face login required" }}
function HF([string]$repo,[string]$dest){ Ensure-Dir $dest; & huggingface-cli download "$repo" --local-dir "$dest" --resume-download --local-dir-use-symlinks False; if($LASTEXITCODE -ne 0){ throw "Download failed: $repo" } }
Write-Host "Downloading base model (SD 1.5 compatible)..." -ForegroundColor Cyan
HF "stable-diffusion-v1-5/stable-diffusion-v1-5" (Join-Path $BASE_MODEL_DIR "stable-diffusion-v1-5")
Write-Host "ControlNet..." -ForegroundColor Cyan
HF "lllyasviel/control_v11p_sd15_canny"  (Join-Path $CONTROLNET_DIR "control_v11p_sd15_canny")
HF "lllyasviel/sd-controlnet-openpose"   (Join-Path $CONTROLNET_DIR "sd-controlnet-openpose")
HF "lllyasviel/control_v11f1e_sd15_tile" (Join-Path $CONTROLNET_DIR "control_v11f1e_sd15_tile")
if(-not $SkipPixelModels){
  Write-Host "Pixel models..." -ForegroundColor Cyan
  HF "PublicPrompts/All-In-One-Pixel-Model" (Join-Path $BASE_MODEL_DIR "All-In-One-Pixel-Model")
  HF "kohbanye/pixel-art-style"             (Join-Path $BASE_MODEL_DIR "pixel-art-style")
}
if(-not $SkipLCM){
  Write-Host "LCM LoRA..." -ForegroundColor Cyan
  HF "latent-consistency/lcm-lora-sdv1-5" (Join-Path $LORA_DIR "lcm-lora-sdv1-5")
}
Write-Host "All done. Models in $BaseDir" -ForegroundColor Green
```

---

## 🐳 docker-compose.yml
```yaml
version: "3.9"
services:
  api:
    build: ./backend
    image: rts-pixel-api:latest
    environment:
      - MODEL_ID=/opt/models/base/stable-diffusion-v1-5
      - OUTPUT_DIR=/data/outputs
    volumes:
      - ./models:/opt/models
      - ./outputs:/data/outputs
      - hf_cache:/opt/hf-cache
    ports:
      - "8000:8000"
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    # For Docker Desktop on Windows, GPU pass-through must be enabled.

  web:
    build: ./frontend
    image: rts-pixel-web:latest
    ports:
      - "5173:80"
    depends_on:
      - api

volumes:
  hf_cache:
```

> If you prefer Nginx to proxy `/api` to backend, add an nginx.conf or use Traefik; for local dev, directly call `http://localhost:8000` from the frontend by setting `VITE_API_URL`.

---

## 📄 README.md
```md
# RTS Pixel Sprite Generation Stack

This repo provides a minimal **backend API** to generate pixel-art frames and compose sprite sheets, plus a **Vue 3 UI** to preview & export PNG/JSON and **Godot .tres**.

## Requirements
- Docker Desktop (Windows) with WSL2 & GPU support recommended.
- PowerShell, Python (for first-time `huggingface-cli` login).

## Quick Start
```powershell
# 1) download models
pwsh -File ./scripts/download_models.ps1
# 2) compose up
docker compose up --build
# 3) open UI
http://localhost:5173
# 4) backend health
http://localhost:8000/healthz
```

## Models & Directories
```
models/
  base/
    stable-diffusion-v1-5/              # SD 1.5 compatible mirror
    All-In-One-Pixel-Model/ (optional)
    pixel-art-style/        (optional)
  controlnet/
    control_v11p_sd15_canny/
    sd-controlnet-openpose/
    control_v11f1e_sd15_tile/
  lora/
    lcm-lora-sdv1-5/        (optional)
outputs/
```

### Recommended Models for Pixel RTS
- Base: `stable-diffusion-v1-5/stable-diffusion-v1-5` (lightweight, 8GB friendly)
- ControlNet: `control_v11p_sd15_canny`, `sd-controlnet-openpose`, `control_v11f1e_sd15_tile`
- Pixel-specialized (optional): `PublicPrompts/All-In-One-Pixel-Model`, `kohbanye/pixel-art-style`
- Speed-up (optional): `latent-consistency/lcm-lora-sdv1-5`

## API
- `POST /txt2img` — body: `{ prompt, negative_prompt, steps, guidance, width, height, seed, count, pixel_mode, tile_w, tile_h }` → PNG (first frame)
- `POST /spritesheet` — same fields + `{ cols, rows, padding, bg_color, frame_ms, anim_name, export_godot_tres }` → returns sprite PNG bytes and headers with file names; files saved under `outputs/`.

## Notes
- Width/Height are clamped to multiples of 8.
- Pixel mode applies nearest-neighbor pixelization to enhance retro look.
- Godot `.tres` output is a minimal **SpriteFrames** resource referencing the PNG; you can tweak or import with a small editor script.
```

---

## ▶️ Run Tips (Windows)
- Ensure GPU is available to containers. In Docker Desktop settings, enable **Use GPU support**.
- If `VITE_API_URL` is needed, run the web container with `-e VITE_API_URL=http://host.docker.internal:8000` or adjust Axios baseURL.

---

**Done.** You can copy this whole tree into your repo and `docker compose up --build`.



---

## 🔁 Update: Nginx Reverse Proxy + ControlNet/LoRA Switches

### 1) Add gateway (Nginx) to proxy `/api` and `/` (frontend)

Create **nginx/nginx.conf**
```nginx
user  nginx;
worker_processes  auto;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events { worker_connections  1024; }

http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;
  sendfile        on;
  keepalive_timeout  65;

  # Frontend at / (served by web service)
  upstream web {
    server web:80;
  }
  # Backend FastAPI at /api
  upstream api {
    server api:8000;
  }

  server {
    listen 80;

    # Proxy API
    location /api/ {
      proxy_pass http://api/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static frontend
    location / {
      proxy_pass http://web/;
    }
  }
}
```

Add **gateway** service to `docker-compose.yml` and expose port 80:
```yaml
services:
  gateway:
    image: nginx:1.27-alpine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
    depends_on:
      - api
      - web
```
> After this, open **http://localhost/** (no need to hit 5173/8000). Frontend requests `/api/*` transparently.

---

### 2) Frontend points to `/api` (no change needed)
`frontend/src/api.ts` already defaults to `baseURL: '/api'`. If you prefer explicit env:
```bash
# in compose (web service), you can pass
# -e VITE_API_URL=/api
```

---

### 3) Backend: optional LoRA toggle (apply/unapply) + list

Update **backend/app/main.py** — add LoRA helpers and request fields:
```python
# add near imports
from diffusers.utils import is_xformers_available

LORA_DIR = os.environ.get("LORA_DIR", "/opt/models/lora")
os.makedirs(LORA_DIR, exist_ok=True)

# helpers
def list_loras():
    return [f for f in os.listdir(LORA_DIR) if f.endswith('.safetensors') or f.endswith('.bin')]

def apply_lora(name: Optional[str]):
    if not name:
        try:
            pipe.unload_lora_weights()
        except Exception:
            pass
        return
    path = os.path.join(LORA_DIR, name)
    pipe.load_lora_weights(path)

# extend request models
class GenRequest(BaseModel):
    # ... existing fields ...
    lora_name: Optional[str] = None

class SpriteRequest(GenRequest):
    # inherits generation fields +
    cols: int = 12
    rows: int = 7
    padding: int = 2
    bg_color: str = "transparent"
    frame_ms: int = 80
    anim_name: str = "idle"
    export_godot_tres: bool = True

@app.get('/lora/list')
def lora_list():
    return {"loras": list_loras()}
```

Apply LoRA inside `/txt2img` and `/spritesheet` before generation:
```python
# before generating frames in each endpoint
apply_lora(getattr(req, 'lora_name', None))
```

> ControlNet: exposing run-time switches requires shipping a control image and swapping to `StableDiffusionControlNetPipeline`. To keep API slender for now, we keep base pipeline; in a follow-up we can add `/txt2img_control` that accepts a control image (PNG/base64) + `controlnet_model` path (mounted under `/opt/models/controlnet`).

---

### 4) Compose tweaks for LoRA directory
Add an extra mount for LoRAs:
```yaml
services:
  api:
    environment:
      - MODEL_ID=/opt/models/base/stable-diffusion-v1-5
      - OUTPUT_DIR=/data/outputs
      - LORA_DIR=/opt/models/lora
    volumes:
      - ./models:/opt/models
      - ./outputs:/data/outputs
      - hf_cache:/opt/hf-cache
```

Now your full stack runs at **http://localhost/** with:
- **/api** → FastAPI backend
- **/** → Vue3 UI
- Optional LoRA selection via `lora_name` field in requests and `/api/lora/list` endpoint.


