# RTS Pixel Sprite Generation Stack

This repository provides a complete **backend API** to generate pixel-art frames and compose sprite sheets, plus a **Vue 3 UI** to preview & export PNG/JSON and **Godot .tres** files.

## 🎯 Features

- **AI-Powered Sprite Generation**: Uses Stable Diffusion 1.5 for pixel art generation
- **Sprite Sheet Composition**: Automatically arranges frames into sprite sheets
- **Multiple Export Formats**: PNG, JSON metadata, CSV, and Godot .tres files
- **LoRA Support**: Load and apply LoRA models for specialized styles
- **Pixel Art Optimization**: Built-in pixelization for retro aesthetics
- **Web Interface**: Modern Vue 3 frontend with real-time preview
- **Docker Ready**: Complete containerized deployment

## 📋 Requirements

- **Docker Desktop** (Windows) with WSL2 & GPU support recommended
- **PowerShell** for model download script
- **Python 3.8+** (for huggingface-cli login)
- **8GB+ GPU** recommended (works on CPU but slower)

## 🚀 Quick Start

### 1. Download Models

First, download the required AI models:

```powershell
# Run the model download script
pwsh -File ./scripts/download_models.ps1
```

This will:
- Install `huggingface-cli` if needed
- Prompt for Hugging Face login (required for some models)
- Download Stable Diffusion 1.5 and related models
- Set up the proper directory structure

### 2. Start the Stack

```bash
# Build and start all services
docker compose up --build
```

### 3. Access the Application

- **Web UI**: http://localhost/ (main interface)
- **API Health**: http://localhost/api/healthz
- **Direct API**: http://localhost:8000 (if needed)
- **Direct Frontend**: http://localhost:5173 (if needed)

## 📁 Project Structure

```
rts-pixel-stack/
├─ backend/                   # FastAPI backend
│  ├─ app/
│  │  ├─ main.py             # Main API endpoints
│  │  └─ utils.py            # Sprite sheet utilities
│  ├─ requirements.txt
│  └─ Dockerfile
├─ frontend/                  # Vue 3 frontend
│  ├─ src/
│  │  ├─ App.vue             # Main UI component
│  │  ├─ api.ts              # API client
│  │  └─ main.ts
│  ├─ package.json
│  ├─ vite.config.ts
│  └─ Dockerfile
├─ models/                    # AI models (created by script)
│  ├─ base/                  # Base Stable Diffusion models
│  ├─ controlnet/            # ControlNet models
│  └─ lora/                  # LoRA models
├─ outputs/                   # Generated sprite sheets & metadata
├─ scripts/
│  └─ download_models.ps1    # Model download script
├─ nginx/
│  └─ nginx.conf             # Reverse proxy configuration
├─ docker-compose.yml
└─ README.md
```

## 🎨 Usage

### Basic Workflow

1. **Enter Prompt**: Describe your desired sprite (e.g., "pixel art, RTS unit, medieval knight")
2. **Configure Settings**: Adjust generation parameters, sprite sheet layout
3. **Preview**: Generate a single frame to test your prompt
4. **Generate Sprite Sheet**: Create multiple frames arranged in a sprite sheet
5. **Download**: Get PNG, JSON, CSV, and optionally Godot .tres files

### Key Parameters

- **Prompt**: Describe what you want to generate
- **Negative Prompt**: What to avoid (e.g., "blurry, smooth shading")
- **Steps**: Quality vs speed (20-50 recommended)
- **CFG Scale**: How closely to follow prompt (7-12 recommended)
- **Seed**: For reproducible results (0 = random)
- **Pixel Mode**: Applies pixelization for retro look
- **LoRA**: Optional style models for specialized looks

### Sprite Sheet Settings

- **Cols/Rows**: Grid layout (12x1 for walk cycles, 4x4 for various poses)
- **Padding**: Space between frames
- **Frame Duration**: Animation timing in milliseconds
- **Background**: Transparent or solid color

## 🔧 API Endpoints

- `GET /api/healthz` - Health check
- `GET /api/lora/list` - List available LoRA models
- `POST /api/txt2img` - Generate single image
- `POST /api/spritesheet` - Generate sprite sheet
- `GET /api/files/{filename}` - Download generated files

## 🎮 Godot Integration

The generated `.tres` files are Godot 4 SpriteFrames resources that can be directly imported:

1. Copy the `.png` and `.tres` files to your Godot project
2. Create an AnimatedSprite2D node
3. Assign the `.tres` file to the SpriteFrames property
4. Call `play("animation_name")` to start animation

## 🛠️ Development

### Running Locally

#### Option 1: Full Development Setup
```bash
# 1. Start backend
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 2. In another terminal, start frontend
cd frontend
npm install
npm run dev:local  # Uses development environment with direct backend URL
```

#### Option 2: Frontend Development with Docker Backend
```bash
# 1. Start only backend and gateway with Docker
docker compose up api gateway

# 2. Start frontend locally
cd frontend
npm install
npm run dev  # Uses production environment with /api proxy
```

#### Option 3: Full Docker Development
```bash
# Start everything with Docker
docker compose up --build
```

### Environment Variables

#### Backend Environment Variables
- `MODEL_ID`: Path to Stable Diffusion model
- `OUTPUT_DIR`: Where to save generated files
- `LORA_DIR`: Directory containing LoRA models

#### Frontend Environment Variables
- `VITE_API_URL`: Frontend API base URL
  - Development: `http://localhost:8000` (direct backend)
  - Production: `/api` (via nginx proxy)
  - Custom: `https://your-api-domain.com/api`

#### Frontend Environment Files
- `.env` - Default configuration
- `.env.development` - Development mode settings
- `.env.production` - Production mode settings
- `.env.local` - Local overrides (not tracked in git)
- `.env.example` - Example configuration template

## 📦 Models & Storage

### Recommended Models

- **Base**: `stable-diffusion-v1-5/stable-diffusion-v1-5` (lightweight, 8GB friendly)
- **ControlNet**: `control_v11p_sd15_canny`, `sd-controlnet-openpose`, `control_v11f1e_sd15_tile`
- **Pixel Art**: `PublicPrompts/All-In-One-Pixel-Model`, `kohbanye/pixel-art-style`
- **Speed**: `latent-consistency/lcm-lora-sdv1-5` (LCM LoRA for faster generation)

### Storage Requirements

- Models: ~15-20GB
- Generated outputs: Varies by usage
- Docker images: ~5-8GB

## 🐛 Troubleshooting

### GPU Issues
- Ensure Docker Desktop has GPU support enabled
- Check NVIDIA drivers are up to date
- Verify CUDA compatibility

### Model Download Issues
- Ensure you're logged into Hugging Face: `huggingface-cli login`
- Check internet connection and disk space
- Some models require accepting license agreements on Hugging Face

### Memory Issues
- Reduce image dimensions (384x384 recommended for 8GB GPU)
- Lower batch size (fewer frames at once)
- Use CPU if GPU memory insufficient

## 📄 License

This project is provided as-is for educational and research purposes. Please respect the licenses of the underlying models and dependencies.

## 🤝 Contributing

Contributions welcome! Please feel free to submit issues and pull requests.

---

**Ready to create pixel art sprites for your RTS game!** 🎮✨
