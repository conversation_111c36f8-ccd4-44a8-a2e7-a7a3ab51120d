import os, random, io
from typing import List, Optional
from fastapi import Fast<PERSON><PERSON>, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from PIL import Image
import torch
from diffusers import StableDiffusionPipeline
from .utils import clamp8, pixelize, build_spritesheet, write_godot_tres

MODEL_ID = os.environ.get("MODEL_ID", "stable-diffusion-v1-5/stable-diffusion-v1-5")
OUTPUT_DIR = os.environ.get("OUTPUT_DIR", "/data/outputs")
LORA_DIR = os.environ.get("LORA_DIR", "/opt/models/lora")
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
DTYPE = torch.float16 if DEVICE == "cuda" else torch.float32

os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LORA_DIR, exist_ok=True)

app = FastAPI(title="RTS Pixel Sprite API", version="1.0")

# Configure CORS
# Check if we're in development mode
is_development = os.environ.get("ENVIRONMENT", "development").lower() == "development"
cors_origins = os.environ.get("CORS_ORIGINS")

if cors_origins:
    # Use custom origins from environment variable
    allowed_origins = [origin.strip() for origin in cors_origins.split(",") if origin.strip()]
elif is_development:
    # For development, allow all origins
    allowed_origins = ["*"]
else:
    # Production default - specific origins only
    allowed_origins = [
        "http://localhost:5173",
        "http://localhost:3000",
        "http://localhost:8080",
    ]

print(f"Environment: {'development' if is_development else 'production'}")
print(f"CORS allowed origins: {allowed_origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=False if allowed_origins == ["*"] else True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Global pipeline variable
pipe = None

def init_pipeline():
    global pipe
    if pipe is None:
        print(f"Loading model: {MODEL_ID}")
        pipe = StableDiffusionPipeline.from_pretrained(
            MODEL_ID,
            torch_dtype=DTYPE,
            safety_checker=None,
            requires_safety_checker=False
        )
        pipe = pipe.to(DEVICE)
        if DEVICE == "cuda":
            pipe.enable_attention_slicing()
        print("Pipeline loaded successfully")

class GenRequest(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = None
    steps: int = 28
    guidance: float = 7.5
    width: int = 384
    height: int = 384
    seed: int = 0
    count: int = 1
    pixel_mode: bool = True
    tile_w: int = 64
    tile_h: int = 64
    pixel_keep_size: bool = True
    pixel_scale: int = 2
    lora_name: Optional[str] = None

class SpriteRequest(GenRequest):
    cols: int = 12
    rows: int = 1
    padding: int = 2
    bg_color: str = "transparent"
    frame_ms: int = 80
    anim_name: str = "idle"
    export_godot_tres: bool = True

def list_loras():
    print('lora folder' + LORA_DIR)
    #if not os.path.exists(LORA_DIR):
        return ["dkjfkdjf"]
    #return [f for f in os.listdir(LORA_DIR) if f.endswith('.safetensors') or f.endswith('.bin')]

def apply_lora(name: Optional[str]):
    global pipe
    if not name:
        try:
            pipe.unload_lora_weights()
        except Exception:
            pass
        return
    path = os.path.join(LORA_DIR, name)
    if os.path.exists(path):
        pipe.load_lora_weights(path)

@app.on_event("startup")
async def startup_event():
    init_pipeline()

@app.get("/healthz")
def health():
    return {"status": "ok", "device": DEVICE, "model": MODEL_ID}

@app.get("/lora/list")
def lora_list():
    return {"loras": list_loras()}

@app.post("/txt2img")
def txt2img(req: GenRequest):
    global pipe
    if pipe is None:
        init_pipeline()
    
    # Apply LoRA if specified
    apply_lora(req.lora_name)
    
    # Clamp dimensions
    width = clamp8(req.width)
    height = clamp8(req.height)
    
    # Set seed
    if req.seed > 0:
        generator = torch.Generator(device=DEVICE).manual_seed(req.seed)
    else:
        generator = None
    
    # Generate image
    result = pipe(
        prompt=req.prompt,
        negative_prompt=req.negative_prompt,
        num_inference_steps=req.steps,
        guidance_scale=req.guidance,
        width=width,
        height=height,
        generator=generator,
        num_images_per_prompt=1
    )
    
    img = result.images[0]
    
    # Apply pixelization if requested
    if req.pixel_mode:
        img = pixelize(img, req.tile_w, req.tile_h, req.pixel_keep_size, req.pixel_scale)
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format="PNG")
    img_bytes.seek(0)
    
    return Response(content=img_bytes.getvalue(), media_type="image/png")

@app.post("/spritesheet")
def spritesheet(req: SpriteRequest):
    global pipe
    if pipe is None:
        init_pipeline()
    
    # Apply LoRA if specified
    apply_lora(req.lora_name)
    
    # Clamp dimensions
    width = clamp8(req.width)
    height = clamp8(req.height)
    
    # Generate multiple frames
    images = []
    for i in range(req.count):
        # Use different seeds for variety
        if req.seed > 0:
            seed = req.seed + i
            generator = torch.Generator(device=DEVICE).manual_seed(seed)
        else:
            generator = None
        
        result = pipe(
            prompt=req.prompt,
            negative_prompt=req.negative_prompt,
            num_inference_steps=req.steps,
            guidance_scale=req.guidance,
            width=width,
            height=height,
            generator=generator,
            num_images_per_prompt=1
        )
        
        img = result.images[0]
        
        # Apply pixelization if requested
        if req.pixel_mode:
            img = pixelize(img, req.tile_w, req.tile_h, req.pixel_keep_size, req.pixel_scale)
        
        images.append(img)
    
    # Build sprite sheet
    png_path, json_path, csv_path = build_spritesheet(
        images, req.cols, req.rows, req.tile_w, req.tile_h,
        req.padding, req.bg_color, req.frame_ms, req.anim_name, OUTPUT_DIR
    )
    
    # Generate Godot .tres if requested
    tres_path = None
    if req.export_godot_tres:
        tres_path = png_path.replace('.png', '.tres')
        write_godot_tres(json_path, tres_path)
    
    # Prepare response headers with file names
    headers = {
        "x-sprite-json": os.path.basename(json_path),
        "x-sprite-csv": os.path.basename(csv_path),
    }
    if tres_path:
        headers["x-godot-tres"] = os.path.basename(tres_path)
    
    # Return sprite sheet PNG
    with open(png_path, "rb") as f:
        content = f.read()
    
    return Response(content=content, media_type="image/png", headers=headers)

@app.get("/files/{filename}")
def get_file(filename: str):
    file_path = os.path.join(OUTPUT_DIR, filename)
    if os.path.exists(file_path):
        return FileResponse(file_path)
    return {"error": "File not found"}
