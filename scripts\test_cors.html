<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { margin: 20px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 10px 0; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
    </style>
</head>
<body>
    <h1>RTS Pixel Sprite API - CORS Test</h1>
    
    <div>
        <label for="apiUrl">API URL:</label><br>
        <input type="text" id="apiUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
    </div>
    
    <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
    <button onclick="testLoraList()">Test LoRA List</button>
    
    <div id="results"></div>

    <script>
        function getApiUrl() {
            return document.getElementById('apiUrl').value.trim() || 'http://localhost:8000';
        }

        function addResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testHealthEndpoint() {
            const apiUrl = getApiUrl();
            try {
                addResult(`Testing health endpoint: ${apiUrl}/healthz`);
                const response = await fetch(`${apiUrl}/healthz`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Health check successful: ${JSON.stringify(data)}`, true);
                } else {
                    addResult(`❌ Health check failed: ${response.status} ${response.statusText}`, false);
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, false);
                if (error.message.includes('CORS')) {
                    addResult(`💡 This looks like a CORS issue. Make sure the backend allows requests from ${window.location.origin}`, false);
                }
            }
        }

        async function testLoraList() {
            const apiUrl = getApiUrl();
            try {
                addResult(`Testing LoRA list endpoint: ${apiUrl}/lora/list`);
                const response = await fetch(`${apiUrl}/lora/list`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ LoRA list successful: Found ${data.loras.length} LoRA models`, true);
                } else {
                    addResult(`❌ LoRA list failed: ${response.status} ${response.statusText}`, false);
                }
            } catch (error) {
                addResult(`❌ LoRA list error: ${error.message}`, false);
                if (error.message.includes('CORS')) {
                    addResult(`💡 This looks like a CORS issue. Make sure the backend allows requests from ${window.location.origin}`, false);
                }
            }
        }

        // Auto-test on page load
        window.onload = function() {
            addResult(`🚀 CORS Test Page loaded. Origin: ${window.location.origin}`);
            addResult(`📝 Instructions: Update the API URL above and click the test buttons to verify CORS configuration.`);
        };
    </script>
</body>
</html>
