{"_class_name": "UNet2DConditionModel", "_diffusers_version": "0.11.1", "_name_or_path": "/root/.cache/huggingface/diffusers/models--runwayml--stable-diffusion-v1-5/snapshots/889b629140e71758e1e0006e355c331a5744b4bf/unet", "act_fn": "silu", "attention_head_dim": 8, "block_out_channels": [320, 640, 1280, 1280], "center_input_sample": false, "class_embed_type": null, "cross_attention_dim": 768, "down_block_types": ["CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "DownBlock2D"], "downsample_padding": 1, "dual_cross_attention": false, "flip_sin_to_cos": true, "freq_shift": 0, "in_channels": 4, "layers_per_block": 2, "mid_block_scale_factor": 1, "mid_block_type": "UNetMidBlock2DCrossAttn", "norm_eps": 1e-05, "norm_num_groups": 32, "num_class_embeds": null, "only_cross_attention": false, "out_channels": 4, "resnet_time_scale_shift": "default", "sample_size": 64, "up_block_types": ["UpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D"], "upcast_attention": false, "use_linear_projection": false}