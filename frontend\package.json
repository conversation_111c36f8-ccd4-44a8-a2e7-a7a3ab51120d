{"name": "rts-pixel-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "dev:local": "vite --mode development", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview --port 5173"}, "dependencies": {"axios": "^1.7.2", "vue": "^3.4.38"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "typescript": "^5.5.4", "vite": "^5.4.2"}}