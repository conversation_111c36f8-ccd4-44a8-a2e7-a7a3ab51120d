# Build stage
FROM node:20-alpine as build
WORKDIR /app

# Accept build arguments
ARG VITE_API_URL=/api
ENV VITE_API_URL=$VITE_API_URL

COPY frontend/package.json frontend/package-lock.json* ./
RUN npm i --no-audit --no-fund
COPY frontend .
RUN npm run build

# Serve stage
FROM nginx:1.27-alpine
COPY --from=build /app/dist /usr/share/nginx/html
# simple api proxy (optional): use compose to wire to backend at /api
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
