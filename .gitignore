# Generated models and outputs
models/
outputs/
*.png
*.jpg
*.jpeg
*.safetensors
*.bin
*.ckpt

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Frontend build
frontend/dist/
frontend/.vite/

# Docker
.dockerignore

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Keep example and base env files in git
# .env
# .env.development
# .env.production
# .env.example

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Hugging Face cache (if local)
.cache/
hf_cache/
