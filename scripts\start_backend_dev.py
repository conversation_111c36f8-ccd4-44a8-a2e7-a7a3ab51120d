#!/usr/bin/env python3
"""
Quick development server startup script with CORS debugging
"""
import os
import sys
import subprocess

# Set development environment variables
os.environ["ENVIRONMENT"] = "development"
os.environ["MODEL_ID"] = "stable-diffusion-v1-5/stable-diffusion-v1-5"
os.environ["OUTPUT_DIR"] = "./outputs"
os.environ["LORA_DIR"] = "./models/lora"

# Change to backend directory
backend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "backend")
if os.path.exists(backend_dir):
    os.chdir(backend_dir)
    print(f"Changed to backend directory: {os.getcwd()}")
else:
    print(f"Backend directory not found: {backend_dir}")
    sys.exit(1)

# Create output directories
os.makedirs("./outputs", exist_ok=True)
os.makedirs("./models/lora", exist_ok=True)

print("Starting FastAPI development server with CORS enabled...")
print("Environment: development")
print("CORS: Allow all origins (*)")
print("API will be available at: http://localhost:8000")
print("Health check: http://localhost:8000/healthz")
print("LoRA list: http://localhost:8000/lora/list")
print("\nPress Ctrl+C to stop the server")

try:
    # Start uvicorn with reload
    subprocess.run([
        sys.executable, "-m", "uvicorn", 
        "app.main:app", 
        "--host", "0.0.0.0", 
        "--port", "8000", 
        "--reload",
        "--log-level", "info"
    ])
except KeyboardInterrupt:
    print("\nServer stopped.")
except Exception as e:
    print(f"Error starting server: {e}")
    sys.exit(1)
