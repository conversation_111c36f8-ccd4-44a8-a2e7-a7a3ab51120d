#!/usr/bin/env python3
"""
Simple script to test the backend API and CORS configuration
"""
import requests
import sys

def test_backend(base_url="http://localhost:8000"):
    print(f"Testing backend at: {base_url}")
    
    # Test health endpoint
    try:
        print("\n1. Testing health endpoint...")
        response = requests.get(f"{base_url}/healthz")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        # Check CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        print(f"   CORS Headers: {cors_headers}")
        
    except Exception as e:
        print(f"   Error: {e}")
        return False
    
    # Test LoRA list endpoint
    try:
        print("\n2. Testing LoRA list endpoint...")
        response = requests.get(f"{base_url}/lora/list")
        print(f"   Status: {response.status_code}")
        data = response.json()
        print(f"   Found {len(data.get('loras', []))} LoRA models")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test OPTIONS request (CORS preflight)
    try:
        print("\n3. Testing CORS preflight (OPTIONS)...")
        response = requests.options(f"{base_url}/healthz", headers={
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        })
        print(f"   Status: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        print(f"   CORS Headers: {cors_headers}")
        
        if response.headers.get('Access-Control-Allow-Origin'):
            print("   ✅ CORS appears to be configured correctly")
        else:
            print("   ❌ CORS headers missing")
            
    except Exception as e:
        print(f"   Error: {e}")
    
    return True

if __name__ == "__main__":
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    test_backend(base_url)
