version: "3.9"

services:
  gateway:
    image: nginx:1.27-alpine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
    depends_on:
      - api
      - web
    restart: unless-stopped

  api:
    build: 
      context: .
      dockerfile: ./backend/Dockerfile
    image: rts-pixel-api:latest
    environment:
      - MODEL_ID=/opt/models/base/stable-diffusion-v1-5
      - OUTPUT_DIR=/data/outputs
      - LORA_DIR=/opt/models/lora
    volumes:
      - ./models:/opt/models
      - ./outputs:/data/outputs
      - hf_cache:/opt/hf-cache
    ports:
      - "8000:8000"
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    # For Docker Desktop on Windows, GPU pass-through must be enabled.

  web:
    build:
      context: .
      dockerfile: ./frontend/Dockerfile
      args:
        - VITE_API_URL=/api
    image: rts-pixel-web:latest
    ports:
      - "5173:80"
    depends_on:
      - api
    restart: unless-stopped

volumes:
  hf_cache:
