# Development overrides
# This file is automatically loaded by docker-compose for local development
version: "3.9"

services:
  api:
    # Enable hot reload for development
    volumes:
      - ./backend/app:/app/app:ro
    environment:
      - PYTHONPATH=/app
      # More permissive CORS for development
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:5173,http://127.0.0.1:8080,http://localhost,http://127.0.0.1
    # Expose API directly for frontend development
    ports:
      - "8000:8000"

  web:
    # Build with development environment
    build:
      args:
        - VITE_API_URL=http://localhost:8000
    # Enable hot reload for frontend development
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
