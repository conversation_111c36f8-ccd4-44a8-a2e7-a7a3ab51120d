# Development overrides
# This file is automatically loaded by docker-compose for local development
version: "3.9"

services:
  api:
    # Enable hot reload for development
    volumes:
      - ./backend/app:/app/app:ro
    environment:
      - PYTHONPATH=/app
    # Expose API directly for frontend development
    ports:
      - "8000:8000"

  web:
    # Build with development environment
    build:
      args:
        - VITE_API_URL=http://localhost:8000
    # Enable hot reload for frontend development
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
