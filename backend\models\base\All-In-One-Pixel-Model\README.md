---
license: creativeml-openrail-m
---
Stable Diffusion model trained using dreambooth to create pixel art, in 2 styles
the sprite art can be used with the trigger word "pixelsprite"
the scene art can be used with the trigger word "16bitscene"


the art is not pixel perfect, but it can be fixed with pixelating tools like https://pinetools.com/pixelate-effect-image (they also have bulk pixelation)


some example generations


![03044-**********-godzilla, in pixelsprite style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023237949-63507e5e18a4f616c9dfba19.png)
![00366-443747549-cute_cat_full_bodyin_pixelsprite_style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023239268-63507e5e18a4f616c9dfba19.png)
![02827-0-street in a sunny day. in 16bitscene style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023288054-63507e5e18a4f616c9dfba19.png)
![02829-0-magical alice in wonderland forest, in 16bitscene style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023291263-63507e5e18a4f616c9dfba19.png)
![02831-1-car driving away, synthwave outrun style wallpaper, in 16bitscene style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023267399-63507e5e18a4f616c9dfba19.png)
![02863-7-isometric living room, detailed, in 16bitscene style.png](https://s3.amazonaws.com/moonup/production/uploads/1668023243698-63507e5e18a4f616c9dfba19.png)
![02935-1805121122-dark arcade room, pink neon lights, detailed, in 16bitscene style,.png](https://s3.amazonaws.com/moonup/production/uploads/1668023243346-63507e5e18a4f616c9dfba19.png)
