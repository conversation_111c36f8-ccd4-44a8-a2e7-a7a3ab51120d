{"_class_name": "ControlNetModel", "_diffusers_version": "0.16.0.dev0", "_name_or_path": "/home/<USER>/controlnet_v1_1/control_v11p_sd15_canny", "act_fn": "silu", "attention_head_dim": 8, "block_out_channels": [320, 640, 1280, 1280], "class_embed_type": null, "conditioning_embedding_out_channels": [16, 32, 96, 256], "controlnet_conditioning_channel_order": "rgb", "cross_attention_dim": 768, "down_block_types": ["CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "DownBlock2D"], "downsample_padding": 1, "flip_sin_to_cos": true, "freq_shift": 0, "in_channels": 4, "layers_per_block": 2, "mid_block_scale_factor": 1, "norm_eps": 1e-05, "norm_num_groups": 32, "num_class_embeds": null, "only_cross_attention": false, "projection_class_embeddings_input_dim": null, "resnet_time_scale_shift": "default", "upcast_attention": false, "use_linear_projection": false}